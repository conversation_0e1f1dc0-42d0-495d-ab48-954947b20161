package org.npci.rustyclient.client.connection;

import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannel;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.interceptor.AuthenticationInterceptor;
import org.npci.rustyclient.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Connection pool for RustyCluster gRPC clients.
 */
public class ConnectionPool implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPool.class);

    private final GrpcChannelFactory channelFactory;
    private final AuthenticationManager authenticationManager;
    private final Map<String, GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceBlockingStub>> stubPools;

    /**
     * Create a new ConnectionPool.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public ConnectionPool(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
        this.channelFactory = new GrpcChannelFactory(config);
        this.authenticationManager = authenticationManager;
        this.stubPools = new HashMap<>();

        // Initialize connection pools for each node
        for (NodeConfig nodeConfig : config.getNodes()) {
            GenericObjectPoolConfig<KeyValueServiceGrpc.KeyValueServiceBlockingStub> poolConfig = new GenericObjectPoolConfig<>();

            // High-throughput optimizations
            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());
            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());
            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm

            // Performance-oriented validation
            poolConfig.setTestOnBorrow(false); // Disable for performance - validate on return instead
            poolConfig.setTestOnReturn(false); // Disable expensive validation
            poolConfig.setTestWhileIdle(true); // Only validate idle connections
            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30)); // More frequent cleanup

            // High-throughput settings
            poolConfig.setBlockWhenExhausted(false); // Fail fast instead of blocking
            poolConfig.setMaxWait(java.time.Duration.ofMillis(100)); // Quick timeout
            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer
            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead

            // JMX monitoring for production
            poolConfig.setJmxEnabled(true);
            poolConfig.setJmxNamePrefix("RustyCluster-" + nodeConfig.getAddress());

            GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceBlockingStub> pool =
                new GenericObjectPool<>(new StubFactory(nodeConfig), poolConfig);

            stubPools.put(nodeConfig.getAddress(), pool);
            logger.info("Created connection pool for node: {}", nodeConfig);
        }
    }

    /**
     * Borrow a client stub from the pool for the specified node.
     *
     * @param nodeConfig The node configuration
     * @return A client stub
     * @throws Exception If borrowing fails
     */
    public KeyValueServiceGrpc.KeyValueServiceBlockingStub borrowStub(NodeConfig nodeConfig) throws Exception {
        GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceBlockingStub> pool = stubPools.get(nodeConfig.getAddress());
        if (pool == null) {
            throw new IllegalArgumentException("No pool found for node: " + nodeConfig);
        }
        return pool.borrowObject();
    }

    /**
     * Return a client stub to the pool.
     *
     * @param nodeConfig The node configuration
     * @param stub       The client stub to return
     */
    public void returnStub(NodeConfig nodeConfig, KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) {
        GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceBlockingStub> pool = stubPools.get(nodeConfig.getAddress());
        if (pool == null) {
            logger.warn("No pool found for node: {}", nodeConfig);
            return;
        }
        pool.returnObject(stub);
    }

    /**
     * Get the authentication manager.
     *
     * @return The authentication manager
     */
    public AuthenticationManager getAuthenticationManager() {
        return authenticationManager;
    }

    /**
     * Close the connection pool and release all resources.
     */
    @Override
    public void close() {
        for (GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceBlockingStub> pool : stubPools.values()) {
            pool.close();
        }
        stubPools.clear();
        channelFactory.close(); // Close cached channels
        logger.info("Connection pool closed");
    }

    /**
     * Factory for creating and validating client stubs.
     */
    private class StubFactory extends BasePooledObjectFactory<KeyValueServiceGrpc.KeyValueServiceBlockingStub> {
        private final NodeConfig nodeConfig;

        StubFactory(NodeConfig nodeConfig) {
            this.nodeConfig = nodeConfig;
        }

        @Override
        public KeyValueServiceGrpc.KeyValueServiceBlockingStub create() {
            ManagedChannel channel = channelFactory.createChannel(nodeConfig);

            // Create channel with authentication interceptor
            var interceptedChannel = ClientInterceptors.intercept(channel,
                    new AuthenticationInterceptor(authenticationManager));

            // Don't set deadline here - it will be set per-operation to avoid
            // "ClientCall started after CallOptions deadline was exceeded" errors
            return KeyValueServiceGrpc.newBlockingStub(interceptedChannel);
        }

        @Override
        public PooledObject<KeyValueServiceGrpc.KeyValueServiceBlockingStub> wrap(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) {
            return new DefaultPooledObject<>(stub);
        }

        @Override
        public boolean validateObject(PooledObject<KeyValueServiceGrpc.KeyValueServiceBlockingStub> p) {
            // In a real implementation, you might want to perform a health check
            // For now, we'll just return true
            return true;
        }

        @Override
        public void destroyObject(PooledObject<KeyValueServiceGrpc.KeyValueServiceBlockingStub> p) {
            KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = p.getObject();
            ManagedChannel channel = (ManagedChannel) stub.getChannel();
            try {
                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                logger.warn("Interrupted while shutting down channel", e);
                Thread.currentThread().interrupt();
            }
        }
    }
}
