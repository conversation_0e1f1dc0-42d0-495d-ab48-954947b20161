package org.npci.rustyclient.client;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.npci.rustyclient.client.BatchOperationBuilder;
import org.npci.rustyclient.grpc.RustyClusterProto;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class BatchOperationBuilderTest {

    @Test
    @DisplayName("Should build an empty list when no operations are added")
    void shouldBuildEmptyListWhenNoOperationsAdded() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).isEmpty();
    }

    @Test
    @DisplayName("Should add a SET operation")
    void shouldAddSetOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addSet("key", "value");
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.SET);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getValue()).isEqualTo("value");
    }

    @Test
    @DisplayName("Should add a DELETE operation")
    void shouldAddDeleteOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addDelete("key");
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.DELETE);
        assertThat(operation.getKey()).isEqualTo("key");
    }

    @Test
    @DisplayName("Should add a SETEX operation")
    void shouldAddSetExOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addSetEx("key", "value", 60);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.SETEX);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getValue()).isEqualTo("value");
        assertThat(operation.getTtl()).isEqualTo(60);
    }

    @Test
    @DisplayName("Should add a SETEXPIRY operation")
    void shouldAddSetExpiryOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addSetExpiry("key", 60);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.SETEXPIRY);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getTtl()).isEqualTo(60);
    }

    @Test
    @DisplayName("Should add an INCRBY operation")
    void shouldAddIncrByOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addIncrBy("key", 10);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.INCRBY);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getIntValue()).isEqualTo(10);
    }

    @Test
    @DisplayName("Should add a DECRBY operation")
    void shouldAddDecrByOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addDecrBy("key", 10);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.DECRBY);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getIntValue()).isEqualTo(10);
    }

    @Test
    @DisplayName("Should add an INCRBYFLOAT operation")
    void shouldAddIncrByFloatOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addIncrByFloat("key", 10.5);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.INCRBYFLOAT);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getFloatValue()).isEqualTo(10.5);
    }

    @Test
    @DisplayName("Should add an HSET operation")
    void shouldAddHSetOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addHSet("key", "field", "value");
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.HSET);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getField()).isEqualTo("field");
        assertThat(operation.getValue()).isEqualTo("value");
    }

    @Test
    @DisplayName("Should add an HINCRBY operation")
    void shouldAddHIncrByOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addHIncrBy("key", "field", 10);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.HINCRBY);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getField()).isEqualTo("field");
        assertThat(operation.getIntValue()).isEqualTo(10);
    }

    @Test
    @DisplayName("Should add an HDECRBY operation")
    void shouldAddHDecrByOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addHDecrBy("key", "field", 10);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.HDECRBY);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getField()).isEqualTo("field");
        assertThat(operation.getIntValue()).isEqualTo(10);
    }

    @Test
    @DisplayName("Should add an HINCRBYFLOAT operation")
    void shouldAddHIncrByFloatOperation() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder.addHIncrByFloat("key", "field", 10.5);
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(1);
        RustyClusterProto.BatchOperation operation = operations.get(0);
        assertThat(operation.getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.HINCRBYFLOAT);
        assertThat(operation.getKey()).isEqualTo("key");
        assertThat(operation.getField()).isEqualTo("field");
        assertThat(operation.getFloatValue()).isEqualTo(10.5);
    }

    @Test
    @DisplayName("Should chain multiple operations")
    void shouldChainMultipleOperations() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // When
        builder
                .addSet("key1", "value1")
                .addDelete("key2")
                .addSetEx("key3", "value3", 60)
                .addHSet("key4", "field", "value");
        List<RustyClusterProto.BatchOperation> operations = builder.build();

        // Then
        assertThat(operations).hasSize(4);
        assertThat(operations.get(0).getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.SET);
        assertThat(operations.get(1).getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.DELETE);
        assertThat(operations.get(2).getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.SETEX);
        assertThat(operations.get(3).getOperationType()).isEqualTo(RustyClusterProto.BatchOperation.OperationType.HSET);
    }

    @Test
    @DisplayName("Should create a new list on each build call")
    void shouldCreateNewListOnEachBuildCall() {
        // Given
        BatchOperationBuilder builder = new BatchOperationBuilder()
                .addSet("key", "value");

        // When
        List<RustyClusterProto.BatchOperation> operations1 = builder.build();
        List<RustyClusterProto.BatchOperation> operations2 = builder.build();

        // Then
        assertThat(operations1).isNotSameAs(operations2);
        assertThat(operations1).hasSize(1);
        assertThat(operations2).hasSize(1);
    }
}
