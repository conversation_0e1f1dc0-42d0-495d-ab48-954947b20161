package org.npci.rustyclient.client.auth;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.grpc.KeyValueServiceGrpc;
import org.npci.rustyclient.grpc.RustyClusterProto;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Tests for AuthenticationManager.
 */
class AuthenticationManagerTest {

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStub;

    private AuthenticationManager authenticationManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("Should return true when no authentication is configured")
    void shouldReturnTrueWhenNoAuthenticationConfigured() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .build();
        authenticationManager = new AuthenticationManager(config);

        // When
        boolean result = authenticationManager.authenticate(mockStub);

        // Then
        assertThat(result).isTrue();
        assertThat(authenticationManager.isAuthenticated()).isFalse();
        assertThat(authenticationManager.getSessionToken()).isNull();
    }

    @Test
    @DisplayName("Should authenticate successfully when credentials are provided")
    void shouldAuthenticateSuccessfullyWhenCredentialsProvided() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .authentication("testuser", "testpass")
                .build();
        authenticationManager = new AuthenticationManager(config);

        // Mock the authenticate response
        RustyClusterProto.AuthenticateResponse mockResponse = RustyClusterProto.AuthenticateResponse.newBuilder()
                .setSuccess(true)
                .setSessionToken("test-session-token")
                .setMessage("Authentication successful")
                .build();
        when(mockStub.authenticate(any(RustyClusterProto.AuthenticateRequest.class))).thenReturn(mockResponse);

        // When
        boolean result = authenticationManager.authenticate(mockStub);

        // Then
        assertThat(result).isTrue();
        assertThat(authenticationManager.isAuthenticated()).isTrue();
        assertThat(authenticationManager.getSessionToken()).isNotNull();
        assertThat(authenticationManager.getSessionToken()).isEqualTo("test-session-token");
    }

    @Test
    @DisplayName("Should clear authentication state")
    void shouldClearAuthenticationState() {
        // Given
        RustyClusterClientConfig config = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .authentication("testuser", "testpass")
                .build();
        authenticationManager = new AuthenticationManager(config);

        // Mock the authenticate response
        RustyClusterProto.AuthenticateResponse mockResponse = RustyClusterProto.AuthenticateResponse.newBuilder()
                .setSuccess(true)
                .setSessionToken("test-session-token")
                .setMessage("Authentication successful")
                .build();
        when(mockStub.authenticate(any(RustyClusterProto.AuthenticateRequest.class))).thenReturn(mockResponse);

        authenticationManager.authenticate(mockStub);

        // When
        authenticationManager.clearAuthentication();

        // Then
        assertThat(authenticationManager.isAuthenticated()).isFalse();
        assertThat(authenticationManager.getSessionToken()).isNull();
    }

    @Test
    @DisplayName("Should check if authentication is configured")
    void shouldCheckIfAuthenticationIsConfigured() {
        // Given - config without authentication
        RustyClusterClientConfig configWithoutAuth = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .build();

        // Given - config with authentication
        RustyClusterClientConfig configWithAuth = RustyClusterClientConfig.builder()
                .addNodes("localhost:50051")
                .authentication("user", "pass")
                .build();

        // Then
        assertThat(configWithoutAuth.hasAuthentication()).isFalse();
        assertThat(configWithAuth.hasAuthentication()).isTrue();
    }
}
