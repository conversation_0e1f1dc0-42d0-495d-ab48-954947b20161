package org.npci.rustyclient.client;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import rustycluster.Rustycluster;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Builder for creating batch operations.
 */
public class BatchOperationBuilder {
    private static final Logger logger = LoggerFactory.getLogger(BatchOperationBuilder.class);
    private final List<Rustycluster.BatchOperation> operations = new ArrayList<>();

    /**
     * Create a new BatchOperationBuilder.
     */
    public BatchOperationBuilder() {
    }

    /**
     * Add a SET operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @return The builder instance
     */
    public BatchOperationBuilder addSet(String key, String value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SET)
                .setKey(key)
                .setValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a DELETE operation to the batch.
     *
     * @param key The key
     * @return The builder instance
     */
    public BatchOperationBuilder addDelete(String key) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DELETE)
                .setKey(key)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a SETEX operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetEx(String key, String value, long ttl) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEX)
                .setKey(key)
                .setValue(value)
                .setTtl(ttl)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a SETEXPIRY operation to the batch.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return The builder instance
     */
    public BatchOperationBuilder addSetExpiry(String key, long ttl) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.SETEXPIRY)
                .setKey(key)
                .setTtl(ttl)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an INCRBY operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrBy(String key, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBY)
                .setKey(key)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add a DECRBY operation to the batch.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addDecrBy(String key, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.DECRBY)
                .setKey(key)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an INCRBYFLOAT operation to the batch.
     *
     * @param key   The key
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addIncrByFloat(String key, double value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.INCRBYFLOAT)
                .setKey(key)
                .setFloatValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HSET operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return The builder instance
     */
    public BatchOperationBuilder addHSet(String key, String field, String value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HSET)
                .setKey(key)
                .setField(field)
                .setValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HINCRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrBy(String key, String field, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBY)
                .setKey(key)
                .setField(field)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HDECRBY operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The builder instance
     */
    public BatchOperationBuilder addHDecrBy(String key, String field, long value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HDECRBY)
                .setKey(key)
                .setField(field)
                .setIntValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    /**
     * Add an HINCRBYFLOAT operation to the batch.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The builder instance
     */
    public BatchOperationBuilder addHIncrByFloat(String key, String field, double value) {
        RustyClusterProto.BatchOperation operation = RustyClusterProto.BatchOperation.newBuilder()
                .setOperationType(RustyClusterProto.BatchOperation.OperationType.HINCRBYFLOAT)
                .setKey(key)
                .setField(field)
                .setFloatValue(value)
                .build();
        operations.add(operation);
        return this;
    }

    // ==================== NEW BATCH OPERATIONS ====================

    /**
     * Add an HMSET operation to the batch.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return The builder instance
     */
    public BatchOperationBuilder addHMSet(String key, Map<String, String> fields) {
        // For now, add individual HSET operations until HMSET is available in BatchOperation
        for (Map.Entry<String, String> entry : fields.entrySet()) {
            addHSet(key, entry.getKey(), entry.getValue());
        }
        return this;
    }

    /**
     * Add a SETNX operation to the batch.
     *
     * @param key   The key
     * @param value The value
     * @return The builder instance
     */
    public BatchOperationBuilder addSetNX(String key, String value) {
        // Note: This will be implemented properly when SETNX is available in BatchOperation.OperationType
        // For now, we'll use a regular SET operation as a placeholder
        logger.warn("SETNX batch operation not fully implemented yet - using SET as placeholder");
        return addSet(key, value);
    }

    /**
     * Add a LOAD_SCRIPT operation to the batch.
     *
     * @param script The Lua script to load
     * @return The builder instance
     */
    public BatchOperationBuilder addLoadScript(String script) {
        // Note: This will be implemented when LOAD_SCRIPT is available in BatchOperation.OperationType
        logger.warn("LOAD_SCRIPT batch operation not fully implemented yet - requires gRPC class regeneration");
        return this;
    }

    /**
     * Add an EVALSHA operation to the batch.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return The builder instance
     */
    public BatchOperationBuilder addEvalSha(String sha, List<String> keys, List<String> args) {
        // Note: This will be implemented when EVALSHA is available in BatchOperation.OperationType
        logger.warn("EVALSHA batch operation not fully implemented yet - requires gRPC class regeneration");
        return this;
    }

    /**
     * Build the list of batch operations.
     *
     * @return The list of batch operations
     */
    public List<Rustycluster.BatchOperation> build() {
        return new ArrayList<>(operations);
    }
}
