package org.npci.rustyclient.client.connection;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.NodeRole;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.AsyncConnectionPool;
import org.npci.rustyclient.client.connection.AsyncFailbackManager;
import org.npci.rustyclient.grpc.KeyValueServiceGrpc;
import org.npci.rustyclient.grpc.RustyClusterProto;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AsyncFailbackAuthenticationTest {

    @Mock
    private AsyncConnectionPool connectionPool;

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceFutureStub mockStub;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceFutureStub mockStubWithDeadline;

    private RustyClusterClientConfig config;
    private List<NodeConfig> sortedNodes;
    private AtomicReference<NodeConfig> currentNode;

    @BeforeEach
    void setUp() {
        // Create config with authentication enabled
        config = RustyClusterClientConfig.builder()
                .addNode("primary", 50051, NodeRole.PRIMARY)
                .addNode("secondary", 50052, NodeRole.SECONDARY)
                .authentication("testuser", "testpass")
                .enableFailback(true)
                .failbackCheckInterval(100, TimeUnit.MILLISECONDS) // Fast interval for testing
                .failbackHealthCheckRetries(1)
                .build();

        // Create sorted nodes (primary has higher priority)
        NodeConfig primaryNode = new NodeConfig("primary", 50051, NodeRole.PRIMARY);
        NodeConfig secondaryNode = new NodeConfig("secondary", 50052, NodeRole.SECONDARY);
        sortedNodes = List.of(primaryNode, secondaryNode);

        // Start with secondary node (simulating after failover)
        currentNode = new AtomicReference<>(secondaryNode);

        // Mock connection pool to return authentication manager
        lenient().when(connectionPool.getAuthenticationManager()).thenReturn(authenticationManager);
    }

    @Test
    @DisplayName("Should clear authentication when failing back to primary node asynchronously")
    void shouldClearAuthenticationWhenFailingBackToPrimaryNodeAsync() throws Exception {
        NodeConfig primaryNode = sortedNodes.get(0);
        NodeConfig secondaryNode = sortedNodes.get(1);

        // Mock primary node as healthy
        when(connectionPool.borrowStubAsync(primaryNode))
                .thenReturn(CompletableFuture.completedFuture(mockStub));
        when(mockStub.withDeadlineAfter(eq(1000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);

        // Note: For authenticated servers, the async health check just checks connection
        // establishment and doesn't perform the actual get operation

        try (AsyncFailbackManager failbackManager = new AsyncFailbackManager(
                config, connectionPool, sortedNodes, currentNode)) {

            // Verify we start with secondary node
            assertThat(currentNode.get()).isEqualTo(secondaryNode);

            // Manually trigger failback check (simulating the scheduled check)
            failbackManager.start();

            // Wait a bit for the async failback check to complete
            Thread.sleep(300);

            // Verify that we switched back to primary node
            assertThat(currentNode.get()).isEqualTo(primaryNode);

            // Verify that authentication was cleared when switching back
            verify(authenticationManager, atLeastOnce()).clearAuthentication();
        }
    }

    @Test
    @DisplayName("Should not clear authentication when no async failback occurs")
    void shouldNotClearAuthenticationWhenNoAsyncFailbackOccurs() throws Exception {
        NodeConfig primaryNode = sortedNodes.get(0);

        // Mock primary node as unhealthy (connection fails)
        when(connectionPool.borrowStubAsync(primaryNode))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Connection failed")));

        try (AsyncFailbackManager failbackManager = new AsyncFailbackManager(
                config, connectionPool, sortedNodes, currentNode)) {

            failbackManager.start();

            // Wait a bit for the async failback check to complete
            Thread.sleep(300);

            // Verify that authentication was NOT cleared since no failback occurred
            verify(authenticationManager, never()).clearAuthentication();
        }
    }

    @Test
    @DisplayName("Should not clear authentication when authentication is disabled in async mode")
    void shouldNotClearAuthenticationWhenAuthenticationDisabledAsync() throws Exception {
        // Create config without authentication
        RustyClusterClientConfig configNoAuth = RustyClusterClientConfig.builder()
                .addNode("primary", 50051, NodeRole.PRIMARY)
                .addNode("secondary", 50052, NodeRole.SECONDARY)
                .enableFailback(true)
                .failbackCheckInterval(100, TimeUnit.MILLISECONDS)
                .failbackHealthCheckRetries(1)
                .build();

        NodeConfig primaryNode = sortedNodes.get(0);

        // Mock primary node as healthy
        when(connectionPool.borrowStubAsync(primaryNode))
                .thenReturn(CompletableFuture.completedFuture(mockStub));
        when(mockStub.withDeadlineAfter(eq(1000L), eq(TimeUnit.MILLISECONDS)))
                .thenReturn(mockStubWithDeadline);

        // Mock the ListenableFuture response
        ListenableFuture<RustyClusterProto.GetResponse> mockResponse =
                Futures.immediateFuture(RustyClusterProto.GetResponse.newBuilder().build());
        when(mockStubWithDeadline.get(any(RustyClusterProto.GetRequest.class)))
                .thenReturn(mockResponse);

        try (AsyncFailbackManager failbackManager = new AsyncFailbackManager(
                configNoAuth, connectionPool, sortedNodes, currentNode)) {

            failbackManager.start();

            // Wait a bit for the async failback check to complete
            Thread.sleep(300);

            // Verify that authentication was NOT cleared since authentication is disabled
            verify(authenticationManager, never()).clearAuthentication();
        }
    }
}
